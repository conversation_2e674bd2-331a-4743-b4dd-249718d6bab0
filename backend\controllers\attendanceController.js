const Attendance = require('../models/Attendance');
const User = require('../models/User');

// Check in
const checkIn = async (req, res) => {
  try {
    const { location, notes } = req.body;
    const userId = req.user._id;
    const employeeId = req.user.employeeId;
    
    // Get current date (start of day)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Check if already checked in today
    let attendance = await Attendance.findOne({
      userId,
      date: today
    });

    if (attendance && attendance.checkIn.time) {
      return res.status(400).json({
        success: false,
        message: 'Already checked in today',
        data: { checkInTime: attendance.checkIn.time }
      });
    }

    // Get client IP
    const ipAddress = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;

    if (!attendance) {
      // Create new attendance record
      attendance = new Attendance({
        userId,
        employeeId,
        date: today,
        checkIn: {
          time: new Date(),
          location: location || '',
          ipAddress,
          notes: notes || ''
        }
      });
    } else {
      // Update existing record
      attendance.checkIn = {
        time: new Date(),
        location: location || '',
        ipAddress,
        notes: notes || ''
      };
    }

    await attendance.save();

    res.json({
      success: true,
      message: 'Check-in successful',
      data: {
        attendance,
        checkInTime: attendance.checkIn.time,
        isLate: attendance.isLate,
        lateMinutes: attendance.lateMinutes
      }
    });

  } catch (error) {
    console.error('Check-in error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during check-in'
    });
  }
};

// Check out
const checkOut = async (req, res) => {
  try {
    const { location, notes } = req.body;
    const userId = req.user._id;
    
    // Get current date (start of day)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Find today's attendance record
    const attendance = await Attendance.findOne({
      userId,
      date: today
    });

    if (!attendance || !attendance.checkIn.time) {
      return res.status(400).json({
        success: false,
        message: 'Must check in first'
      });
    }

    if (attendance.checkOut.time) {
      return res.status(400).json({
        success: false,
        message: 'Already checked out today',
        data: { checkOutTime: attendance.checkOut.time }
      });
    }

    // Get client IP
    const ipAddress = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;

    // Update check-out information
    attendance.checkOut = {
      time: new Date(),
      location: location || '',
      ipAddress,
      notes: notes || ''
    };

    await attendance.save();

    res.json({
      success: true,
      message: 'Check-out successful',
      data: {
        attendance,
        checkOutTime: attendance.checkOut.time,
        workingHours: attendance.workingHours,
        overtimeHours: attendance.overtimeHours
      }
    });

  } catch (error) {
    console.error('Check-out error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during check-out'
    });
  }
};

// Get today's attendance status
const getTodayStatus = async (req, res) => {
  try {
    const userId = req.user._id;
    
    // Get current date (start of day)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const attendance = await Attendance.findOne({
      userId,
      date: today
    });

    if (!attendance) {
      return res.json({
        success: true,
        data: {
          hasCheckedIn: false,
          hasCheckedOut: false,
          attendance: null
        }
      });
    }

    res.json({
      success: true,
      data: {
        hasCheckedIn: !!attendance.checkIn.time,
        hasCheckedOut: !!attendance.checkOut.time,
        attendance
      }
    });

  } catch (error) {
    console.error('Get today status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get attendance history
const getAttendanceHistory = async (req, res) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10, startDate, endDate, status } = req.query;

    // Build query
    const query = { userId };

    // Date range filter
    if (startDate || endDate) {
      query.date = {};
      if (startDate) {
        query.date.$gte = new Date(startDate);
      }
      if (endDate) {
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        query.date.$lte = end;
      }
    }

    // Status filter
    if (status) {
      query.status = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [attendances, total] = await Promise.all([
      Attendance.find(query)
        .sort({ date: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('userId', 'fullName employeeId'),
      Attendance.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        attendances,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalRecords: total,
          hasNext: skip + attendances.length < total,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get attendance history error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get attendance statistics
const getAttendanceStats = async (req, res) => {
  try {
    const userId = req.user._id;
    const { month, year } = req.query;

    // Default to current month/year
    const currentDate = new Date();
    const targetMonth = month ? parseInt(month) - 1 : currentDate.getMonth();
    const targetYear = year ? parseInt(year) : currentDate.getFullYear();

    // Get start and end of month
    const startOfMonth = new Date(targetYear, targetMonth, 1);
    const endOfMonth = new Date(targetYear, targetMonth + 1, 0, 23, 59, 59, 999);

    const attendances = await Attendance.find({
      userId,
      date: { $gte: startOfMonth, $lte: endOfMonth }
    });

    // Calculate statistics
    const stats = {
      totalDays: attendances.length,
      presentDays: attendances.filter(a => a.status === 'present' || a.status === 'late').length,
      absentDays: attendances.filter(a => a.status === 'absent').length,
      lateDays: attendances.filter(a => a.isLate).length,
      totalWorkingHours: attendances.reduce((sum, a) => sum + (a.workingHours || 0), 0),
      totalOvertimeHours: attendances.reduce((sum, a) => sum + (a.overtimeHours || 0), 0),
      averageWorkingHours: 0,
      totalLateMinutes: attendances.reduce((sum, a) => sum + (a.lateMinutes || 0), 0)
    };

    if (stats.presentDays > 0) {
      stats.averageWorkingHours = stats.totalWorkingHours / stats.presentDays;
    }

    res.json({
      success: true,
      data: {
        month: targetMonth + 1,
        year: targetYear,
        stats
      }
    });

  } catch (error) {
    console.error('Get attendance stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  checkIn,
  checkOut,
  getTodayStatus,
  getAttendanceHistory,
  getAttendanceStats
};
