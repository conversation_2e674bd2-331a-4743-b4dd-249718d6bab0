const mongoose = require('mongoose');

const attendanceSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  employeeId: {
    type: String,
    required: [true, 'Employee ID is required']
  },
  date: {
    type: Date,
    required: [true, 'Date is required'],
    default: function() {
      // Set to start of current day
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return today;
    }
  },
  checkIn: {
    time: {
      type: Date,
      default: null
    },
    location: {
      type: String,
      default: ''
    },
    ipAddress: {
      type: String,
      default: ''
    },
    notes: {
      type: String,
      default: ''
    }
  },
  checkOut: {
    time: {
      type: Date,
      default: null
    },
    location: {
      type: String,
      default: ''
    },
    ipAddress: {
      type: String,
      default: ''
    },
    notes: {
      type: String,
      default: ''
    }
  },
  workingHours: {
    type: Number,
    default: 0,
    get: function(value) {
      return Math.round(value * 100) / 100; // Round to 2 decimal places
    }
  },
  status: {
    type: String,
    enum: ['present', 'absent', 'late', 'half-day', 'holiday', 'leave'],
    default: 'absent'
  },
  isLate: {
    type: Boolean,
    default: false
  },
  lateMinutes: {
    type: Number,
    default: 0
  },
  overtimeHours: {
    type: Number,
    default: 0,
    get: function(value) {
      return Math.round(value * 100) / 100;
    }
  },
  breaks: [{
    startTime: {
      type: Date,
      required: true
    },
    endTime: {
      type: Date,
      default: null
    },
    duration: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      enum: ['lunch', 'coffee', 'other'],
      default: 'other'
    }
  }],
  totalBreakTime: {
    type: Number,
    default: 0
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  approvalStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  comments: {
    type: String,
    default: ''
  }
}, {
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Calculate working hours when check-out is recorded
attendanceSchema.methods.calculateWorkingHours = function() {
  if (this.checkIn.time && this.checkOut.time) {
    const checkInTime = new Date(this.checkIn.time);
    const checkOutTime = new Date(this.checkOut.time);
    const diffInMs = checkOutTime - checkInTime;
    const diffInHours = diffInMs / (1000 * 60 * 60);
    
    // Subtract break time
    const totalBreakHours = this.totalBreakTime / 60; // Convert minutes to hours
    this.workingHours = Math.max(0, diffInHours - totalBreakHours);
    
    // Calculate overtime (assuming 8 hours is standard)
    this.overtimeHours = Math.max(0, this.workingHours - 8);
  }
};

// Calculate if employee is late (assuming 9:00 AM is standard start time)
attendanceSchema.methods.calculateLateness = function() {
  if (this.checkIn.time) {
    const checkInTime = new Date(this.checkIn.time);
    const standardStartTime = new Date(this.date);
    standardStartTime.setHours(9, 0, 0, 0); // 9:00 AM
    
    if (checkInTime > standardStartTime) {
      this.isLate = true;
      this.lateMinutes = Math.round((checkInTime - standardStartTime) / (1000 * 60));
    } else {
      this.isLate = false;
      this.lateMinutes = 0;
    }
  }
};

// Update status based on attendance data
attendanceSchema.methods.updateStatus = function() {
  if (this.checkIn.time) {
    if (this.isLate) {
      this.status = 'late';
    } else {
      this.status = 'present';
    }
  } else {
    this.status = 'absent';
  }
};

// Pre-save middleware to calculate values
attendanceSchema.pre('save', function(next) {
  this.calculateLateness();
  this.calculateWorkingHours();
  this.updateStatus();
  next();
});

// Compound index for efficient queries
attendanceSchema.index({ userId: 1, date: 1 }, { unique: true });
attendanceSchema.index({ employeeId: 1, date: 1 });
attendanceSchema.index({ date: 1 });

module.exports = mongoose.model('Attendance', attendanceSchema);
