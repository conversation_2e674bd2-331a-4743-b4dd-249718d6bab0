const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { initializeTestData } = require('./utils/mockDb');

// Load environment variables
dotenv.config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Initialize mock database
initializeTestData();

console.log('Using Mock Database (In-Memory)');
console.log('Test user: <EMAIL> / password: 123456');

// Routes
app.get('/', (req, res) => {
  res.json({ 
    message: 'Attendance Management System API (Mock Mode)',
    testUser: {
      email: '<EMAIL>',
      password: '123456'
    }
  });
});

// Import routes with mock models
const authRoutes = require('./routes/auth-mock');
const attendanceRoutes = require('./routes/attendance-mock');

// Use routes
app.use('/api/auth', authRoutes);
app.use('/api/attendance', attendanceRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ message: 'Route not found' });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT} (Mock Mode)`);
  console.log(`API available at: http://localhost:${PORT}/api`);
});

module.exports = app;
