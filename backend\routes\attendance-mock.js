const express = require('express');
const router = express.Router();
const { MockAttendance } = require('../utils/mockDb');

// Simple auth middleware for mock
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: 'Access token is required' 
      });
    }

    const jwt = require('jsonwebtoken');
    const { MockUser } = require('../utils/mockDb');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const user = await MockUser.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid token - user not found' 
      });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({ 
      success: false, 
      message: 'Invalid token' 
    });
  }
};

// Check in
const checkIn = async (req, res) => {
  try {
    const { location, notes } = req.body;
    const userId = req.user._id;
    const employeeId = req.user.employeeId;
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let attendance = await MockAttendance.findOne({
      userId,
      date: today
    });

    if (attendance && attendance.checkIn.time) {
      return res.status(400).json({
        success: false,
        message: 'Already checked in today',
        data: { checkInTime: attendance.checkIn.time }
      });
    }

    const ipAddress = req.ip || req.connection.remoteAddress || '127.0.0.1';

    if (!attendance) {
      attendance = new MockAttendance({
        userId,
        employeeId,
        date: today,
        checkIn: {
          time: new Date(),
          location: location || '',
          ipAddress,
          notes: notes || ''
        }
      });
    } else {
      attendance.checkIn = {
        time: new Date(),
        location: location || '',
        ipAddress,
        notes: notes || ''
      };
    }

    await attendance.save();

    res.json({
      success: true,
      message: 'Check-in successful',
      data: {
        attendance,
        checkInTime: attendance.checkIn.time,
        isLate: attendance.isLate,
        lateMinutes: attendance.lateMinutes
      }
    });

  } catch (error) {
    console.error('Check-in error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during check-in'
    });
  }
};

// Check out
const checkOut = async (req, res) => {
  try {
    const { location, notes } = req.body;
    const userId = req.user._id;
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const attendance = await MockAttendance.findOne({
      userId,
      date: today
    });

    if (!attendance || !attendance.checkIn.time) {
      return res.status(400).json({
        success: false,
        message: 'Must check in first'
      });
    }

    if (attendance.checkOut.time) {
      return res.status(400).json({
        success: false,
        message: 'Already checked out today',
        data: { checkOutTime: attendance.checkOut.time }
      });
    }

    const ipAddress = req.ip || req.connection.remoteAddress || '127.0.0.1';

    attendance.checkOut = {
      time: new Date(),
      location: location || '',
      ipAddress,
      notes: notes || ''
    };

    await attendance.save();

    res.json({
      success: true,
      message: 'Check-out successful',
      data: {
        attendance,
        checkOutTime: attendance.checkOut.time,
        workingHours: attendance.workingHours,
        overtimeHours: attendance.overtimeHours
      }
    });

  } catch (error) {
    console.error('Check-out error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during check-out'
    });
  }
};

// Get today's attendance status
const getTodayStatus = async (req, res) => {
  try {
    const userId = req.user._id;
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const attendance = await MockAttendance.findOne({
      userId,
      date: today
    });

    if (!attendance) {
      return res.json({
        success: true,
        data: {
          hasCheckedIn: false,
          hasCheckedOut: false,
          attendance: null
        }
      });
    }

    res.json({
      success: true,
      data: {
        hasCheckedIn: !!attendance.checkIn.time,
        hasCheckedOut: !!attendance.checkOut.time,
        attendance
      }
    });

  } catch (error) {
    console.error('Get today status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get attendance history
const getAttendanceHistory = async (req, res) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10, startDate, endDate, status } = req.query;

    const query = { userId };

    if (startDate || endDate) {
      query.date = {};
      if (startDate) {
        query.date.$gte = new Date(startDate);
      }
      if (endDate) {
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        query.date.$lte = end;
      }
    }

    if (status) {
      query.status = status;
    }

    const attendances = await MockAttendance.find(query);
    const total = attendances.length;
    
    // Simple pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const paginatedAttendances = attendances
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(skip, skip + parseInt(limit));

    res.json({
      success: true,
      data: {
        attendances: paginatedAttendances,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalRecords: total,
          hasNext: skip + paginatedAttendances.length < total,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get attendance history error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Routes
router.use(authenticateToken);
router.post('/check-in', checkIn);
router.post('/check-out', checkOut);
router.get('/today', getTodayStatus);
router.get('/history', getAttendanceHistory);

module.exports = router;
