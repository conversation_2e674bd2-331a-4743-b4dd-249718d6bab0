# Hướng dẫn thiết lập Database

## Lỗi hiện tại
Hệ thống đang báo lỗi "Server error during login" vì không thể kết nối đến MongoDB.

## Giải pháp 1: Sử dụng MongoDB Atlas (Khuyến nghị - Miễn phí)

### Bước 1: Tạo tài khoản MongoDB Atlas
1. Truy cập: https://www.mongodb.com/atlas
2. Đăng ký tài khoản miễn phí
3. Tạo cluster mới (chọn FREE tier)

### Bước 2: Lấy connection string
1. Trong MongoDB Atlas, click "Connect"
2. <PERSON>ọn "Connect your application"
3. Copy connection string (dạng: `mongodb+srv://username:<EMAIL>/`)

### Bước 3: Cập nhật file .env
```env
MONGODB_URI=mongodb+srv://your_username:<EMAIL>/attendance_system
```

## Gi<PERSON>i pháp 2: Cài đặt MongoDB Local

### Windows (với Laragon)
1. Mở Laragon
2. Menu > Tools > Quick add > MongoDB
3. Start MongoDB service
4. Giữ nguyên cấu hình trong .env:
```env
MONGODB_URI=mongodb://localhost:27017/attendance_system
```

### Windows (Manual)
1. Download MongoDB Community Server: https://www.mongodb.com/try/download/community
2. Cài đặt và start MongoDB service
3. Giữ nguyên cấu hình trong .env

### macOS
```bash
brew install mongodb-community
brew services start mongodb-community
```

### Linux (Ubuntu)
```bash
sudo apt update
sudo apt install mongodb
sudo systemctl start mongodb
sudo systemctl enable mongodb
```

## Kiểm tra kết nối
Sau khi thiết lập, restart backend server:
```bash
cd backend
npm run dev
```

Nếu thành công, bạn sẽ thấy: "MongoDB connected successfully"
