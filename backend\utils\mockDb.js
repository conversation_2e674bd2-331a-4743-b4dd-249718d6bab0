// Mock database for testing without MongoDB
const bcrypt = require('bcryptjs');
const { generateToken } = require('./jwt');

// In-memory storage
let users = [];
let attendances = [];
let userIdCounter = 1;
let attendanceIdCounter = 1;

// Mock User model
class MockUser {
  constructor(userData) {
    this._id = userIdCounter++;
    this.username = userData.username;
    this.email = userData.email;
    this.password = userData.password;
    this.fullName = userData.fullName;
    this.employeeId = userData.employeeId;
    this.department = userData.department;
    this.position = userData.position;
    this.role = userData.role || 'employee';
    this.isActive = userData.isActive !== false;
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }

  async save() {
    // Hash password if modified
    if (this.password && !this.password.startsWith('$2a$')) {
      const salt = await bcrypt.genSalt(12);
      this.password = await bcrypt.hash(this.password, salt);
    }
    
    // Check if user already exists
    const existingUser = users.find(u => 
      u.email === this.email || 
      u.username === this.username || 
      u.employeeId === this.employeeId
    );
    
    if (existingUser && existingUser._id !== this._id) {
      const error = new Error('User already exists');
      error.name = 'ValidationError';
      throw error;
    }
    
    // Add or update user
    const index = users.findIndex(u => u._id === this._id);
    if (index >= 0) {
      users[index] = this;
    } else {
      users.push(this);
    }
    
    return this;
  }

  async comparePassword(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
  }

  toJSON() {
    const userObject = { ...this };
    delete userObject.password;
    return userObject;
  }

  static async findOne(query) {
    const user = users.find(u => {
      if (query.$or) {
        return query.$or.some(condition => {
          return Object.keys(condition).every(key => u[key] === condition[key]);
        });
      }
      return Object.keys(query).every(key => u[key] === query[key]);
    });
    
    return user ? Object.assign(new MockUser({}), user) : null;
  }

  static async findById(id) {
    const user = users.find(u => u._id == id);
    return user ? Object.assign(new MockUser({}), user) : null;
  }

  static async findByIdAndUpdate(id, updateData, options = {}) {
    const user = users.find(u => u._id == id);
    if (!user) return null;
    
    Object.assign(user, updateData);
    user.updatedAt = new Date();
    
    return options.new ? Object.assign(new MockUser({}), user) : user;
  }
}

// Mock Attendance model
class MockAttendance {
  constructor(attendanceData) {
    this._id = attendanceIdCounter++;
    this.userId = attendanceData.userId;
    this.employeeId = attendanceData.employeeId;
    this.date = attendanceData.date || new Date();
    this.checkIn = attendanceData.checkIn || { time: null, location: '', ipAddress: '', notes: '' };
    this.checkOut = attendanceData.checkOut || { time: null, location: '', ipAddress: '', notes: '' };
    this.workingHours = attendanceData.workingHours || 0;
    this.status = attendanceData.status || 'absent';
    this.isLate = attendanceData.isLate || false;
    this.lateMinutes = attendanceData.lateMinutes || 0;
    this.overtimeHours = attendanceData.overtimeHours || 0;
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }

  calculateLateness() {
    if (this.checkIn.time) {
      const checkInTime = new Date(this.checkIn.time);
      const standardStartTime = new Date(this.date);
      standardStartTime.setHours(9, 0, 0, 0); // 9:00 AM
      
      if (checkInTime > standardStartTime) {
        this.isLate = true;
        this.lateMinutes = Math.round((checkInTime - standardStartTime) / (1000 * 60));
      } else {
        this.isLate = false;
        this.lateMinutes = 0;
      }
    }
  }

  calculateWorkingHours() {
    if (this.checkIn.time && this.checkOut.time) {
      const checkInTime = new Date(this.checkIn.time);
      const checkOutTime = new Date(this.checkOut.time);
      const diffInMs = checkOutTime - checkInTime;
      const diffInHours = diffInMs / (1000 * 60 * 60);
      
      this.workingHours = Math.max(0, diffInHours);
      this.overtimeHours = Math.max(0, this.workingHours - 8);
    }
  }

  updateStatus() {
    if (this.checkIn.time) {
      if (this.isLate) {
        this.status = 'late';
      } else {
        this.status = 'present';
      }
    } else {
      this.status = 'absent';
    }
  }

  async save() {
    this.calculateLateness();
    this.calculateWorkingHours();
    this.updateStatus();
    
    const index = attendances.findIndex(a => a._id === this._id);
    if (index >= 0) {
      attendances[index] = this;
    } else {
      attendances.push(this);
    }
    
    return this;
  }

  static async findOne(query) {
    const attendance = attendances.find(a => {
      return Object.keys(query).every(key => {
        if (key === 'date' && query[key] instanceof Date) {
          const queryDate = new Date(query[key]);
          const attendanceDate = new Date(a[key]);
          return queryDate.toDateString() === attendanceDate.toDateString();
        }
        return a[key] == query[key];
      });
    });
    
    return attendance ? Object.assign(new MockAttendance({}), attendance) : null;
  }

  static async find(query = {}) {
    let filtered = attendances;
    
    Object.keys(query).forEach(key => {
      if (key === 'date' && query[key].$gte && query[key].$lte) {
        filtered = filtered.filter(a => {
          const date = new Date(a.date);
          return date >= query[key].$gte && date <= query[key].$lte;
        });
      } else if (query[key] && typeof query[key] === 'object' && !query[key].$gte) {
        // Handle other complex queries
      } else {
        filtered = filtered.filter(a => a[key] == query[key]);
      }
    });
    
    return filtered.map(a => Object.assign(new MockAttendance({}), a));
  }

  static async countDocuments(query = {}) {
    const results = await this.find(query);
    return results.length;
  }
}

// Initialize with a test user
const initializeTestData = async () => {
  if (users.length === 0) {
    const testUser = new MockUser({
      username: 'admin',
      email: '<EMAIL>',
      password: '123456',
      fullName: 'Administrator',
      employeeId: 'EMP001',
      department: 'IT',
      position: 'Admin',
      role: 'admin'
    });
    
    await testUser.save();
    console.log('Test user created: <EMAIL> / 123456');
  }
};

module.exports = {
  MockUser,
  MockAttendance,
  initializeTestData,
  isConnected: () => true // Always return true for mock
};
