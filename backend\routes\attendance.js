const express = require('express');
const router = express.Router();
const { authenticateToken, authorizeR<PERSON>s, authorizeOwnerOrAdmin } = require('../middleware/auth');
const {
  checkIn,
  checkOut,
  getTodayStatus,
  getAttendanceHistory,
  getAttendanceStats
} = require('../controllers/attendanceController');

// All attendance routes require authentication
router.use(authenticateToken);

// Employee attendance actions
router.post('/check-in', checkIn);
router.post('/check-out', checkOut);
router.get('/today', getTodayStatus);
router.get('/history', getAttendanceHistory);
router.get('/stats', getAttendanceStats);

// Admin routes for managing attendance
router.get('/admin/users', authorizeRoles('admin', 'hr'), async (req, res) => {
  try {
    const User = require('../models/User');
    const { page = 1, limit = 10, search, department } = req.query;

    const query = { isActive: true };
    
    if (search) {
      query.$or = [
        { fullName: { $regex: search, $options: 'i' } },
        { employeeId: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    if (department) {
      query.department = department;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [users, total] = await Promise.all([
      User.find(query)
        .select('-password')
        .sort({ fullName: 1 })
        .skip(skip)
        .limit(parseInt(limit)),
      User.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalRecords: total,
          hasNext: skip + users.length < total,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

router.get('/admin/user/:userId/history', authorizeRoles('admin', 'hr'), async (req, res) => {
  try {
    const Attendance = require('../models/Attendance');
    const { userId } = req.params;
    const { page = 1, limit = 10, startDate, endDate, status } = req.query;

    const query = { userId };

    if (startDate || endDate) {
      query.date = {};
      if (startDate) {
        query.date.$gte = new Date(startDate);
      }
      if (endDate) {
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        query.date.$lte = end;
      }
    }

    if (status) {
      query.status = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [attendances, total] = await Promise.all([
      Attendance.find(query)
        .sort({ date: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('userId', 'fullName employeeId department'),
      Attendance.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        attendances,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalRecords: total,
          hasNext: skip + attendances.length < total,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get user attendance history error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

router.get('/admin/reports/daily', authorizeRoles('admin', 'hr'), async (req, res) => {
  try {
    const Attendance = require('../models/Attendance');
    const { date } = req.query;
    
    const targetDate = date ? new Date(date) : new Date();
    targetDate.setHours(0, 0, 0, 0);
    
    const endDate = new Date(targetDate);
    endDate.setHours(23, 59, 59, 999);

    const attendances = await Attendance.find({
      date: { $gte: targetDate, $lte: endDate }
    }).populate('userId', 'fullName employeeId department');

    const summary = {
      totalEmployees: attendances.length,
      present: attendances.filter(a => a.status === 'present' || a.status === 'late').length,
      absent: attendances.filter(a => a.status === 'absent').length,
      late: attendances.filter(a => a.isLate).length,
      onTime: attendances.filter(a => a.checkIn.time && !a.isLate).length
    };

    res.json({
      success: true,
      data: {
        date: targetDate,
        summary,
        attendances
      }
    });

  } catch (error) {
    console.error('Get daily report error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
