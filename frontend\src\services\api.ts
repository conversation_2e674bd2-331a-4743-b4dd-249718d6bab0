import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData: {
    username: string;
    email: string;
    password: string;
    fullName: string;
    employeeId: string;
    department: string;
    position: string;
  }) => api.post('/auth/register', userData),

  login: (credentials: { login: string; password: string }) =>
    api.post('/auth/login', credentials),

  getProfile: () => api.get('/auth/profile'),

  updateProfile: (profileData: {
    fullName?: string;
    department?: string;
    position?: string;
  }) => api.put('/auth/profile', profileData),

  changePassword: (passwordData: {
    currentPassword: string;
    newPassword: string;
  }) => api.put('/auth/change-password', passwordData),
};

// Attendance API
export const attendanceAPI = {
  checkIn: (data: { location?: string; notes?: string }) =>
    api.post('/attendance/check-in', data),

  checkOut: (data: { location?: string; notes?: string }) =>
    api.post('/attendance/check-out', data),

  getTodayStatus: () => api.get('/attendance/today'),

  getHistory: (params: {
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
    status?: string;
  }) => api.get('/attendance/history', { params }),

  getStats: (params: { month?: number; year?: number }) =>
    api.get('/attendance/stats', { params }),
};

// Admin API
export const adminAPI = {
  getUsers: (params: {
    page?: number;
    limit?: number;
    search?: string;
    department?: string;
  }) => api.get('/attendance/admin/users', { params }),

  getUserHistory: (
    userId: string,
    params: {
      page?: number;
      limit?: number;
      startDate?: string;
      endDate?: string;
      status?: string;
    }
  ) => api.get(`/attendance/admin/user/${userId}/history`, { params }),

  getDailyReport: (params: { date?: string }) =>
    api.get('/attendance/admin/reports/daily', { params }),
};

export default api;
